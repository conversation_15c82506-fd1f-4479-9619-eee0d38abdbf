#!/usr/bin/env python3

import requests
import json
import time

# NSE URLs and headers
url_oc = "https://www.nseindia.com/option-chain"
url_stock = "https://www.nseindia.com/api/option-chain-equities?symbol="

headers = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'accept-language': 'en,gu;q=0.9,hi;q=0.8',
    'accept-encoding': 'gzip, deflate, br',
    'accept': '*/*',
    'referer': 'https://www.nseindia.com/option-chain',
    'x-requested-with': 'XMLHttpRequest',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin'
}

def test_nse_stock_api():
    print("Testing NSE Stock Options API...")
    
    # Create session
    session = requests.Session()
    session.headers.update(headers)
    
    try:
        # Step 1: Get cookies from homepage
        print("Step 1: Getting cookies from NSE homepage...")
        home_response = session.get("https://www.nseindia.com/", timeout=15)
        print(f"Homepage status: {home_response.status_code}")
        
        if home_response.status_code != 200:
            print(f"Failed to access homepage: {home_response.text[:200]}")
            return
        
        # Step 2: Visit option chain page
        print("Step 2: Visiting option chain page...")
        time.sleep(2)
        oc_response = session.get(url_oc, timeout=15)
        print(f"Option chain status: {oc_response.status_code}")
        
        if oc_response.status_code != 200:
            print(f"Failed to access option chain: {oc_response.text[:200]}")
            return
        
        cookies = dict(session.cookies)
        print(f"Got {len(cookies)} cookies: {list(cookies.keys())}")
        
        # Step 3: Test stock options API
        print("Step 3: Testing INFY stock options...")
        time.sleep(2)
        stock_url = url_stock + "INFY"
        print(f"Requesting: {stock_url}")
        
        response = session.get(stock_url, headers=headers, timeout=15, cookies=cookies)
        print(f"Stock options status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
        print(f"Content-Encoding: {response.headers.get('content-encoding', 'N/A')}")
        print(f"Content length: {len(response.content)}")
        
        if response.status_code != 200:
            print(f"Error response: {response.text[:500]}")
            return
        
        # Try to decompress and parse
        content_encoding = response.headers.get('content-encoding', '').lower()
        text_content = ""
        
        if content_encoding == 'br':
            try:
                import brotli
                decompressed = brotli.decompress(response.content)
                text_content = decompressed.decode('utf-8')
                print("Successfully decompressed brotli content")
            except Exception as e:
                print(f"Brotli decompression failed: {e}")
                text_content = response.text
        elif content_encoding == 'gzip':
            try:
                import gzip
                decompressed = gzip.decompress(response.content)
                text_content = decompressed.decode('utf-8')
                print("Successfully decompressed gzip content")
            except Exception as e:
                print(f"Gzip decompression failed: {e}")
                text_content = response.text
        else:
            text_content = response.text
            print("No compression detected")
        
        # Try to parse JSON
        try:
            if text_content:
                data = json.loads(text_content)
            else:
                data = response.json()
            
            print("Successfully parsed JSON!")
            
            # Check data structure
            records = data.get('records', {})
            print(f"Records keys: {list(records.keys())}")
            
            if 'underlyingValue' in records:
                print(f"Spot price: {records['underlyingValue']}")
            
            if 'expiryDates' in records:
                print(f"Available expiry dates: {records['expiryDates']}")
            
            if 'data' in records:
                print(f"Number of data records: {len(records['data'])}")
                if records['data']:
                    sample_record = records['data'][0]
                    print(f"Sample record keys: {list(sample_record.keys())}")
                    if 'expiryDate' in sample_record:
                        print(f"Sample expiry date: {sample_record['expiryDate']}")
            
        except Exception as e:
            print(f"JSON parsing failed: {e}")
            print(f"Response preview: {text_content[:500] if text_content else response.text[:500]}")
            
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_nse_stock_api()
